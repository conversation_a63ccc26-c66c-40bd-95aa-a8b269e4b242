#!/usr/bin/env python
import os
import sys
import django
from datetime import date

# Configurar Django
sys.path.append('.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'controle_estoque.settings')
django.setup()

from estoque.views import calcular_totais_por_mes_para_tabela

# Dados de teste
vendas_teste = [
    {'data': date(2025, 5, 22), 'quantidade': 1000, 'tipo': 'Pedido', 'numero_pedido': 'P001', 'observacoes': 'Teste 1'},
    {'data': date(2025, 5, 7), 'quantidade': 2000, 'tipo': 'Pedido', 'numero_pedido': 'P002', 'observacoes': 'Teste 2'},
    {'data': date(2025, 6, 15), 'quantidade': 500, 'tipo': 'Pedido', 'numero_pedido': 'P003', 'observacoes': 'Teste 3'},
]

print("Testando função calcular_totais_por_mes_para_tabela...")
print("Vendas de teste:")
for i, venda in enumerate(vendas_teste):
    print(f"  {i}: {venda['data']} - {venda['quantidade']}")

totais = calcular_totais_por_mes_para_tabela(vendas_teste)
print("\nTotais por mês:")
for chave, info in totais.items():
    print(f"  {chave}: {info}")

print("\nTeste concluído!")
