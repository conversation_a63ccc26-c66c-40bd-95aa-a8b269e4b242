{% extends 'estoque/base.html' %}
{% load static %}

{% block title %}Relatório de Vendas por Mola{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Relatório de Vendas por Mola</h1>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Filtros</h6>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-md-6">
                                <label for="{{ form.mola.id_for_label }}" class="form-label">{{ form.mola.label }}</label>
                                {{ form.mola.errors }}
                                {{ form.mola }}
                            </div>
                            <div class="col-md-3">
                                <label for="{{ form.formato.id_for_label }}" class="form-label">{{ form.formato.label }}</label>
                                {{ form.formato.errors }}
                                {{ form.formato }}
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> Gerar Relatório
                                </button>
                            </div>
                        </div>

                        <!-- Período Personalizado -->
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <div class="form-check">
                                    {{ form.usar_periodo_personalizado }}
                                    <label class="form-check-label" for="{{ form.usar_periodo_personalizado.id_for_label }}">
                                        {{ form.usar_periodo_personalizado.label }}
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Campos de período (inicialmente ocultos) -->
                        <div class="row mt-3 periodo-campos" style="display: {% if form.usar_periodo_personalizado.value %}block{% else %}none{% endif %};">
                            <div class="col-md-3">
                                <label for="{{ form.mes_inicial.id_for_label }}" class="form-label">{{ form.mes_inicial.label }}</label>
                                {{ form.mes_inicial.errors }}
                                {{ form.mes_inicial }}
                            </div>
                            <div class="col-md-3">
                                <label for="{{ form.ano_inicial.id_for_label }}" class="form-label">{{ form.ano_inicial.label }}</label>
                                {{ form.ano_inicial.errors }}
                                {{ form.ano_inicial }}
                            </div>
                            <div class="col-md-3">
                                <label for="{{ form.mes_final.id_for_label }}" class="form-label">{{ form.mes_final.label }}</label>
                                {{ form.mes_final.errors }}
                                {{ form.mes_final }}
                            </div>
                            <div class="col-md-3">
                                <label for="{{ form.ano_final.id_for_label }}" class="form-label">{{ form.ano_final.label }}</label>
                                {{ form.ano_final.errors }}
                                {{ form.ano_final }}
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    {% if mola and vendas %}
    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        Vendas da Mola: {{ mola.codigo }} - {{ mola.cliente }}
                        {% if periodo_texto %} - {{ periodo_texto }}{% endif %}
                    </h6>
                </div>
                <div class="card-body">
                    {% if vendas %}
                        {% if usar_periodo %}
                            <!-- Exibição organizada por mês -->
                            {% for mes_data in vendas %}
                                <div class="card mb-3">
                                    <div class="card-header bg-light">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span class="text-muted font-weight-normal">
                                                <i class="fas fa-calendar-alt"></i> {{ mes_data.mes_ano }}
                                            </span>
                                            {% if mes_data.tem_vendas %}
                                                <div>
                                                    <span class="badge badge-primary">{{ mes_data.total_quantidade }} unidades</span>
                                                    <span class="badge badge-secondary ml-1">{{ mes_data.total_transacoes }} transações</span>
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="card-body py-2">
                                        {% if mes_data.tem_vendas %}
                                            <div class="table-responsive">
                                                <table class="table table-sm table-striped mb-0">
                                                    <thead>
                                                        <tr>
                                                            <th>Data</th>
                                                            <th>Tipo</th>
                                                            <th>Número do Pedido</th>
                                                            <th>Quantidade</th>
                                                            <th>Total por Mês</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        {% for venda in mes_data.vendas %}
                                                        <tr>
                                                            <td>{{ venda.data|date:"d/m/Y" }}</td>
                                                            <td>
                                                                {% if venda.tipo == 'Pedido' %}
                                                                    <span class="badge badge-success">{{ venda.tipo }}</span>
                                                                {% else %}
                                                                    <span class="badge badge-info">{{ venda.tipo }}</span>
                                                                {% endif %}
                                                            </td>
                                                            <td>{{ venda.numero_pedido }}</td>
                                                            <td>{{ venda.quantidade }}</td>
                                                            <td style="vertical-align: middle; text-align: center;">
                                                                {% if forloop.first %}
                                                                    <span class="text-primary font-weight-bold">{{ mes_data.mes_ano|slice:":3" }}/{{ mes_data.mes_ano|slice:"-4:" }} - {{ mes_data.total_quantidade }}</span>
                                                                {% endif %}
                                                            </td>
                                                        </tr>
                                                        {% endfor %}
                                                    </tbody>
                                                </table>
                                            </div>
                                        {% else %}
                                            <div class="text-muted pl-2">
                                                <i class="fas fa-info-circle"></i>
                                                Nenhuma venda registrada neste mês
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endfor %}
                        {% else %}
                            <!-- Exibição tradicional (sem agrupamento) -->
                            <div class="table-responsive">
                                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>Data</th>
                                            <th>Tipo</th>
                                            <th>Número do Pedido</th>
                                            <th>Quantidade</th>
                                            <th>Total por Mês</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for venda in vendas %}
                                        <tr>
                                            <td>{{ venda.data|date:"d/m/Y" }}</td>
                                            <td>
                                                {% if venda.tipo == 'Pedido' %}
                                                    <span class="badge badge-success">{{ venda.tipo }}</span>
                                                {% else %}
                                                    <span class="badge badge-info">{{ venda.tipo }}</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ venda.numero_pedido }}</td>
                                            <td class="text-right">{{ venda.quantidade }}</td>
                                            <td style="vertical-align: middle; text-align: center;">
                                                {% if totais_por_mes %}
                                                    {% for chave_mes, info_mes in totais_por_mes.items %}
                                                        {% if forloop.parentloop.counter0 == info_mes.indice_central %}
                                                            <span class="text-primary font-weight-bold">{{ info_mes.texto_total }}</span>
                                                        {% endif %}
                                                    {% endfor %}
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% endif %}
                    
                    <!-- Resumo -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card border-left-primary shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                Total de Transações
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                {% if usar_periodo %}
                                                    {% with total_transacoes=0 %}
                                                        {% for mes_data in vendas %}
                                                            {% with total_transacoes=total_transacoes|add:mes_data.total_transacoes %}{% endwith %}
                                                        {% endfor %}
                                                        {{ total_transacoes }}
                                                    {% endwith %}
                                                {% else %}
                                                    {{ vendas|length }}
                                                {% endif %}
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-left-success shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                Quantidade Total Vendida
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                {% if usar_periodo %}
                                                    {% with total_quantidade=0 %}
                                                        {% for mes_data in vendas %}
                                                            {% with total_quantidade=total_quantidade|add:mes_data.total_quantidade %}{% endwith %}
                                                        {% endfor %}
                                                        {{ total_quantidade }}
                                                    {% endwith %}
                                                {% else %}
                                                    {% with total_quantidade=0 %}
                                                        {% for venda in vendas %}
                                                            {% with total_quantidade=total_quantidade|add:venda.quantidade %}{% endwith %}
                                                        {% endfor %}
                                                        {{ total_quantidade }}
                                                    {% endwith %}
                                                {% endif %}
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-boxes fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Botões de exportação -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <form method="post" class="d-inline">
                                {% csrf_token %}
                                {{ form.mola.as_hidden }}
                                <input type="hidden" name="formato" value="pdf">
                                <button type="submit" class="btn btn-danger mr-2">
                                    <i class="fas fa-file-pdf"></i> Exportar PDF
                                </button>
                            </form>
                            <form method="post" class="d-inline">
                                {% csrf_token %}
                                {{ form.mola.as_hidden }}
                                <input type="hidden" name="formato" value="csv">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-file-csv"></i> Exportar CSV
                                </button>
                            </form>
                        </div>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        Nenhuma venda encontrada para esta mola.
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

{% block extra_js %}
<script>
$(document).ready(function() {
    // Inicializar DataTable se existir
    if ($('#dataTable').length) {
        $('#dataTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Portuguese-Brasil.json"
            },
            "order": [[ 0, "desc" ]], // Ordenar por data decrescente
            "pageLength": 25
        });
    }

    // Controlar exibição dos campos de período personalizado
    $('#id_usar_periodo_personalizado').change(function() {
        if ($(this).is(':checked')) {
            $('.periodo-campos').show();
        } else {
            $('.periodo-campos').hide();
        }
    });

    // Definir valores padrão para período personalizado
    if ($('#id_usar_periodo_personalizado').is(':checked')) {
        $('.periodo-campos').show();
    }

    // Validação do formulário
    $('form').submit(function(e) {
        if ($('#id_usar_periodo_personalizado').is(':checked')) {
            var mesInicial = $('#id_mes_inicial').val();
            var anoInicial = $('#id_ano_inicial').val();
            var mesFinal = $('#id_mes_final').val();
            var anoFinal = $('#id_ano_final').val();

            if (!mesInicial || !anoInicial || !mesFinal || !anoFinal) {
                e.preventDefault();
                alert('Por favor, preencha todos os campos do período personalizado.');
                return false;
            }

            // Validar se a data inicial não é posterior à final
            var dataInicial = new Date(anoInicial, mesInicial - 1, 1);
            var dataFinal = new Date(anoFinal, mesFinal - 1, 1);

            if (dataInicial > dataFinal) {
                e.preventDefault();
                alert('A data inicial não pode ser posterior à data final.');
                return false;
            }
        }
    });
});
</script>
{% endblock %}
{% endblock %}
