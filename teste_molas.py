#!/usr/bin/env python
import os
import sys
import django

# Configurar Django
sys.path.append('.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'controle_estoque.settings')
django.setup()

from estoque.models import Mola, MovimentacaoEstoque, ItemPedido

print("Verificando molas no sistema...")
molas = Mola.objects.all()[:5]
for mola in molas:
    print(f"  {mola.id}: {mola.codigo} - {mola.cliente}")

if molas:
    mola_teste = molas[0]
    print(f"\nTestando com mola: {mola_teste.codigo}")
    
    # Verificar movimentações
    movs = MovimentacaoEstoque.objects.filter(mola=mola_teste, tipo='S')[:3]
    print(f"Movimentações de saída: {movs.count()}")
    for mov in movs:
        print(f"  {mov.data} - {mov.quantidade}")
    
    # Verificar itens de pedido
    itens = ItemPedido.objects.filter(mola=mola_teste, atendido=True)[:3]
    print(f"Itens de pedido atendidos: {itens.count()}")
    for item in itens:
        print(f"  {item.pedido.data_pedido} - {item.quantidade}")

print("\nTeste concluído!")
