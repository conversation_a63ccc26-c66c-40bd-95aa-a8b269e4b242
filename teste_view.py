#!/usr/bin/env python
import os
import sys
import django

# Configurar Django
sys.path.append('.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'controle_estoque.settings')
django.setup()

from estoque.models import Mola
from estoque.views import obter_vendas_mola_especifica, calcular_totais_por_mes_para_tabela

print("Testando view completa...")

# Pegar uma mola para teste
mola = Mola.objects.first()
print(f"Testando com mola: {mola.codigo}")

# Obter vendas
vendas = obter_vendas_mola_especifica(mola)
print(f"Vendas encontradas: {len(vendas)}")

if vendas:
    print("Primeiras vendas:")
    for i, venda in enumerate(vendas[:3]):
        print(f"  {i}: {venda['data']} - {venda['quantidade']} - {venda['tipo']}")
    
    # Calcular totais por mês
    totais = calcular_totais_por_mes_para_tabela(vendas)
    print(f"\nTotais por mês: {len(totais)} meses")
    for chave, info in totais.items():
        print(f"  {chave}: Total {info['total']}, Índice central: {info['indice_central']}")

print("\nTeste concluído!")
