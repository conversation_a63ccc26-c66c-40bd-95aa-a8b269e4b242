#!/usr/bin/env python
import os
import sys
import django

# Configurar Django
sys.path.append('.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'controle_estoque.settings')
django.setup()

from django.template import Template, Context
from estoque.models import Mola
from estoque.views import obter_vendas_mola_especifica, calcular_totais_por_mes_para_tabela

print("Testando template...")

# Pegar uma mola para teste
mola = Mola.objects.first()
vendas = obter_vendas_mola_especifica(mola)
totais_por_mes = calcular_totais_por_mes_para_tabela(vendas)

print(f"Mola: {mola.codigo}")
print(f"Vendas: {len(vendas)}")
print(f"Totais por mês: {totais_por_mes}")

# Template simples para teste
template_str = """
{% for venda in vendas %}
    Linha {{ forloop.counter0 }}: {{ venda.data }} - {{ venda.quantidade }}
    {% if totais_por_mes %}
        {% for chave_mes, info_mes in totais_por_mes.items %}
            {% if forloop.parentloop.counter0 == info_mes.indice_central %}
                --> TOTAL: {{ info_mes.texto_total }}
            {% endif %}
        {% endfor %}
    {% endif %}
{% endfor %}
"""

template = Template(template_str)
context = Context({
    'vendas': vendas,
    'totais_por_mes': totais_por_mes
})

resultado = template.render(context)
print("\nResultado do template:")
print(resultado)

print("\nTeste concluído!")
